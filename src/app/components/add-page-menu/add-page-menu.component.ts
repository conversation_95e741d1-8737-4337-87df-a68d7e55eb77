import { ChangeDetectionStrategy, Component, Input, input, inject, computed, signal, ViewContainerRef } from '@angular/core';
import {
    UtilService,
    StyxTranslateService,
    PilotEntryStore,
    helpers,
    PilotDetailInfo,
    StyxAIAssistantService,
    AIWritingAssistantPayload,
    AIWritingAssistant,
    HasPermissionPipe,
    FileSizeDisplayPipe,
    DriveFileIconUrlPipe
} from '@atinc/ngx-styx';
import { CreatePageType, PageTypes } from '@wiki/app/constants/page';
import { DraftInfo, PageInfo } from '@wiki/app/entities/page-info';
import { PageService } from '@wiki/app/services/util/page.service';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { buildPageTree } from '@wiki/app/util/tree';
import { CommonEditingPageStore } from '@wiki/common/stores/editing-page.store';
import { createPage, getDraftGroup } from '@wiki/common/util/create-page';
import { marked } from 'marked';
import { TheiaConverter } from '@atinc/selene';
import { of, take } from 'rxjs';
import { PlaitElement } from '@plait/core';
import { getAfterId } from '@wiki/common/util/tree';
import { ThyDialog, ThyDialogSizes } from 'ngx-tethys/dialog';
import { FileImportComponent } from '@wiki/app/features/page/components';
import { UploadPageOption } from '@wiki/app/entities';
import { FileImportService } from '@wiki/app/services/file-import.service';

@Component({
    selector: 'wiki-add-page-menu',
    templateUrl: './add-page-menu.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [DriveFileIconUrlPipe, FileSizeDisplayPipe, FileImportService, HasPermissionPipe],
    standalone: false
})
export class WikiAddPageMenuComponent {
    private translate = inject(StyxTranslateService);

    @Input() page: PageInfo;

    @Input() homePage: PageInfo;

    @Input() spaceId: string;

    @Input() initPages: PageInfo[];

    @Input() createPageType: CreatePageType;

    allowNewGroup = input<boolean>(true);

    @Input() addPageHandler: (page: PageInfo) => void;

    @Input() addGroupHandler: (page: PageInfo, draftGroup: DraftInfo) => void;

    isGlobal = input<boolean>(false);

    private pilotStore = inject(PilotEntryStore);

    private pagesStore = inject(PagesStore);

    private aiAssistantService = inject(StyxAIAssistantService);

    private viewContainerRef = inject(ViewContainerRef);

    private thyDialog = inject(ThyDialog);

    private fileImportService = inject(FileImportService);

    permissions = computed(() => {
        let _page: PageInfo | PilotDetailInfo;
        if (this.isGlobal()) {
            _page = this.pilotStore.snapshot.detail;
        } else {
            _page = this.page ? this.page : helpers.find(this.pagesStore.state().entities, { _id: this.page?._id });
        }
        return _page?.permissions;
    });

    get currentSpace() {
        return this.pilotStore.snapshot.detail;
    }

    constructor(
        private util: UtilService,
        private pageService: PageService,
        private editingPageStore: CommonEditingPageStore
    ) {}

    addNewPage(pageType: PageTypes = PageTypes.document, content?: Element[] | PlaitElement[]) {
        const pageNodes = buildPageTree(this.initPages);
        let newPage: {
            spaceId: string;
            page: PageInfo;
            pageType: PageTypes;
            createPageType: CreatePageType;
            treePages: PageInfo[];
            content?: Element[] | PlaitElement[];
        } = {
            spaceId: this.spaceId,
            page: this.page || this.homePage,
            createPageType: this.createPageType,
            pageType,
            treePages: pageNodes
        };
        if (content) {
            newPage.content = content;
        }
        createPage(
            newPage,
            {
                pageService: this.pageService,
                editingPageStore: this.editingPageStore,
                util: this.util
            },
            this.translate,
            (page: PageInfo) => {
                this.addPageHandler && this.addPageHandler(page);
            }
        );
    }

    addNewGroup() {
        const pageNodes = buildPageTree(this.initPages);
        const draftGroup = getDraftGroup(this.page, pageNodes, this.createPageType);
        this.addGroupHandler && this.addGroupHandler(this.page, draftGroup);
    }

    openAIWriting(event: Event) {
        const subscription = this.aiAssistantService.aiAssistantIsOpen$.subscribe(state => {
            const isClosed = state === false;
            if (isClosed) {
                subscription.unsubscribe();
            }
        });
        const payload: AIWritingAssistantPayload = {
            origin: event.currentTarget as HTMLElement,
            key: AIWritingAssistant.helpMeWrite,
            action: (content?: string) => {
                const html = marked(content.replace(/\n```/g, '\n\n```'), { gfm: true }) as string;
                const htmlDom = new DOMParser().parseFromString(html, 'text/html');
                const pageContent = TheiaConverter.convertToTheia(Array.from(htmlDom.body.children));
                this.addNewPage(PageTypes.document, pageContent);
                return of(true);
            }
        };
        this.aiAssistantService.open(payload);
    }

    openStencil() {
        const parentId = this.getParentId(this.createPageType);
        const afterId = this.getAfterId(this.createPageType);
        this.pageService.openStencilDetail(undefined, 'page-create', this.viewContainerRef, parentId, afterId);
    }

    openFileImportDialog(createPageType?: CreatePageType) {
        const popoverRef = this.thyDialog.open(FileImportComponent, {
            size: ThyDialogSizes.lg,
            viewContainerRef: this.viewContainerRef
        });

        popoverRef?.componentInstance.selectFiles.pipe(take(1)).subscribe((option: { files: File[]; uploadPageInfo: UploadPageOption }) => {
            const parentId = this.getParentId(createPageType);
            this.fileImportService.uploadPageFile(
                this.currentSpace.identifier,
                parentId,
                option.files,
                option.uploadPageInfo,
                (pageId: string, pages: PageInfo[]) => {
                    this.addPageHandler?.(pages[0]);
                },
                this.getAfterId(createPageType)
            );
            popoverRef.close();
        });
    }

    private getParentId(pageType?: CreatePageType) {
        if (!pageType || pageType === CreatePageType.Child) {
            if (this.isGlobal() || !this.page) {
                return null;
            } else {
                return this.page._id;
            }
        } else {
            return this.page.parent_id;
        }
    }

    private getAfterId(createPageType?: CreatePageType): string | null | undefined {
        let afterId: string | null | undefined = undefined;
        if (createPageType && createPageType !== CreatePageType.Child) {
            const pageNodes = buildPageTree(this.initPages);
            afterId = getAfterId(this.page, createPageType, pageNodes);
        }
        return afterId;
    }
}
