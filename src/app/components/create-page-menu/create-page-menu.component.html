<div class="thy-dropdown-menu">
  @for (option of addPageOptions(); track $index; let i = $index) {
    <a
      thyDropdownMenuItem
      href="javascript:;"
      [thyDropdown]="expand"
      thyTrigger="hover"
      thyPlacement="rightTop"
      thyActiveClass="action-menu-active"
    >
      <styx-awesome-text class="text-truncate title-text" [styxText]="option?.typeName" [styxIcon]="option.icon"></styx-awesome-text>
      <thy-icon thyDropdownMenuItemExtendIcon thyIconName="angle-right" class="ml-1"></thy-icon>
      <thy-dropdown-menu #expand>
        <wiki-add-page-menu
          [initPages]="initPages"
          [page]="page"
          [homePage]="homePage"
          [spaceId]="spaceId"
          [createPageType]="option.type"
          [allowNewGroup]="allowNewGroup"
          [addPageHandler]="addPageHandler"
          [addGroupHandler]="addGroupHandler"
        ></wiki-add-page-menu>
      </thy-dropdown-menu>
    </a>
  }
</div>
