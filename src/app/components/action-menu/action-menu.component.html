@if (!isHeaderActive) {
  <a
    href="javascript:;"
    thyAction
    thyActionIcon="more-vertical"
    styxI18nTracking
    [thyTooltip]="'common.more' | translate"
    [ngClass]="{ active: isActive() }"
    (click)="openActionMenu($event)"
  ></a>
}
@if (isHeaderActive && currentSpace?.permissions | hasPermission: 'page_create') {
  <a
    href="javascript:;"
    class="space-page-create"
    thyGuiderTarget="createPage"
    [ngClass]="{ active: isActive() }"
    (click)="openActionMenu($event)"
  >
    <thy-icon thyIconName="plus-circle-fill"></thy-icon>
  </a>
}

<ng-template #menu>
  <div class="thy-dropdown-menu">
    @if (page()?._id !== homePage?._id && isHeaderActive) {
      <wiki-add-page-menu
        [page]="page()"
        [homePage]="homePage"
        [spaceId]="spaceId"
        [initPages]="pageNodes"
        [createPageType]="createPageType.Child"
        [isGlobal]="true"
        (addPageHandler)="handleAddPage($event)"
        (addGroupHandler)="handleAddGroup($event)"
      ></wiki-add-page-menu>
    }
    @if (page()?._id !== homePage?._id && !isHeaderActive) {
      <wiki-create-page-menu
        [page]="page()"
        [homePage]="homePage"
        [spaceId]="spaceId"
        [initPages]="pageNodes"
        (addPageHandler)="handleAddPage($event)"
        (addGroupHandler)="handleAddGroup($event)"
      ></wiki-create-page-menu>
      <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
    }
    @if (!isHeaderActive) {
      @if (page | isPage) {
        <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
        <a thyDropdownMenuItem href="javascript:;" (click)="newWindowOpen()">
          <thy-icon thyDropdownMenuItemIcon thyIconName="publish"></thy-icon>
          <span thyDropdownMenuItemName translate="styx.openNew"></span>
        </a>
      }
      <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
      <a thyDropdownMenuItem href="javascript:;" [thyDisabled]="!sharingPermission" (click)="onAction('share')">
        <thy-icon thyDropdownMenuItemIcon thyIconName="share"></thy-icon>
        <span thyDropdownMenuItemName translate="wiki.page.sharing.title"></span>
      </a>
      <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
      <a
        thyDropdownMenuItem
        href="javascript:;"
        [thyDisabled]="!(menuOrigin()?.permissions | hasPermission: 'page_edit')"
        (click)="onAction('rename')"
      >
        <thy-icon thyDropdownMenuItemIcon thyIconName="rename"></thy-icon>
        <span thyDropdownMenuItemName translate="common.rename"></span>
      </a>
      <a
        thyDropdownMenuItem
        thyType="danger"
        href="javascript:;"
        [thyDisabled]="!(page()?.permissions | hasPermission: 'page_delete')"
        (click)="deletePage()"
      >
        <thy-icon thyDropdownMenuItemIcon thyIconName="trash"></thy-icon>
        <span thyDropdownMenuItemName translate="common.delete"></span>
      </a>
    }
  </div>
</ng-template>
