import {
    Component,
    computed,
    effect,
    ElementRef,
    EventEmitter,
    inject,
    input,
    Input,
    NgZone,
    OnInit,
    output,
    Output,
    signal,
    TemplateRef,
    ViewChild,
    ViewContainerRef
} from '@angular/core';

import { HasPermissionPipe, PilotEntryStore } from '@atinc/ngx-styx';
import { ActivatedRoute } from '@angular/router';
import {
    DriveFileIconUrlPipe,
    FileSizeDisplayPipe,
    helpers,
    StyxAIAssistantService
} from '@atinc/ngx-styx';
import { UploadPageOption } from '@wiki/app/entities/file-import';
import { FileImportComponent } from '@wiki/app/features/page/components';
import { FileImportService } from '@wiki/app/services/file-import.service';

import { getAfterId } from '@wiki/common/util/tree';
import { ThyDialog, ThyDialogSizes } from 'ngx-tethys/dialog';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { ThyTree, ThyTreeNode, ThyTreeNodeData } from 'ngx-tethys/tree';
import { take } from 'rxjs/operators';
import { CreatePageType, PageTypes } from '../../constants/page';
import { DraftInfo, PageInfo } from '../../entities/page-info';
import { SpaceInfo } from '../../entities/space-info';

import { PageService } from '../../services/util/page.service';
import { PagesStore } from '../../stores/pages.store';

import { fromEvent } from 'rxjs';
export type ActionType = 'rename' | 'delete' | 'add' | 'stencil' | 'import' | 'share';

export class addPageOption {
    type: CreatePageType;
    typeName: string;
    icon?: string;
}

@Component({
    selector: 'wiki-space-action-menu',
    templateUrl: './action-menu.component.html',
    providers: [DriveFileIconUrlPipe, FileSizeDisplayPipe, FileImportService, HasPermissionPipe],
    standalone: false
})
export class SpaceActionMenuComponent implements OnInit {


    @ViewChild('menu', { read: TemplateRef, static: true })
    actionMenuComponent: TemplateRef<any>;

    @Input() isHeaderActive: boolean;

    @Input() spaceId: string;

    page = input<PageInfo | undefined>(undefined);

    @Input() set pageNodes(pages: ThyTreeNodeData[]) {
        this._pageNodes = [...pages];
    }
    get pageNodes() {
        return this._pageNodes;
    }

    pageId = input<string | null>(null);

    @Input() tree: ThyTree;

    isHome = input<boolean>(false);

    @Input() treeNodeNameEdit: (data: ThyTreeNode) => {};

    @Output() selectImportPage: EventEmitter<string> = new EventEmitter();

    openPageSharing = output<PageInfo>();

    _pageNodes: ThyTreeNodeData[];

    isActiveMenuActive = signal(false);

    isAIWritingActive = signal(false);

    isActive = computed(() => {
        return this.isActiveMenuActive() || this.isAIWritingActive();
    });

    isDraft = signal(false);

    public get homePage() {
        return this.pagesStore.snapshot.homePage;
    }

    get currentSpace() {
        return this.pilotStore.snapshot.detail;
    }

    get spaceScopeType() {
        return this.pilotStore.snapshot.detail.scope_type;
    }

    get sharingPermission() {
        const page = this.page();
        const hasSharingPermission = this.hasPermission.transform(page.permissions, 'page_sharing_setting');
        if (page.type === PageTypes.group && hasSharingPermission) {
            return this.hasChildrenSharingPermission();
        }
        return hasSharingPermission;
    }

    menuOrigin = computed(() => {
        if (this.isHome() || this.isDraft()) {
            return this.pilotStore.snapshot.detail;
        } else {
            return this.page() ? this.page() : helpers.find(this.pagesStore.state().entities, { _id: this.pageId() });
        }
    });

    aiAssistantService = inject(StyxAIAssistantService);

    private hasPermission = inject(HasPermissionPipe);

    private pilotStore = inject(PilotEntryStore);

    origin?: HTMLElement;

    createPageType = CreatePageType;

    constructor(
        private popover: ThyPopover,
        private pagesStore: PagesStore,
        private pageService: PageService,
        private route: ActivatedRoute,
        private elementRef: ElementRef,
        private thyDialog: ThyDialog,
        private viewContainerRef: ViewContainerRef,
        private fileImportService: FileImportService,
        private ngZone: NgZone
    ) {
        effect(() => {
            this.updateNodeWrapperClass();
        });
    }

    ngOnInit() {
        this.fileImportService.initialize();
    }

    openFileImportDialog(createPageType?: CreatePageType) {
        const popoverRef = this.thyDialog.open(FileImportComponent, {
            size: ThyDialogSizes.lg,
            viewContainerRef: this.viewContainerRef
        });

        popoverRef?.componentInstance.selectFiles.pipe(take(1)).subscribe((option: { files: File[]; uploadPageInfo: UploadPageOption }) => {
            const parentId = this.getParentId(createPageType);
            this.fileImportService.uploadPageFile(
                this.currentSpace.identifier,
                parentId,
                option.files,
                option.uploadPageInfo,
                (pageId: string) => {
                    this.selectImportPage.emit(pageId);
                },
                this.getAfterId(createPageType)
            );
            popoverRef.close();
        });
    }

    getParentId(pageType?: CreatePageType) {
        if (!pageType || pageType === CreatePageType.Child) {
            if (this.isHome() || (this.isDraft() && !this.page())) {
                return null;
            } else {
                return this.page() ? this.page()._id : this.pageId();
            }
        } else {
            return this.page()?.parent_id;
        }
    }

    getVerifyPermissionNode(pageType: CreatePageType): PageInfo | SpaceInfo {
        let verifyPermissionNode: PageInfo | SpaceInfo;
        if (pageType === CreatePageType.Child) {
            verifyPermissionNode = this.page();
        } else {
            const parentId = this.getParentId(pageType);
            if (parentId) {
                verifyPermissionNode = helpers.find(this.pagesStore.snapshot.entities, { _id: parentId });
            } else {
                verifyPermissionNode = this.currentSpace;
            }
        }
        return verifyPermissionNode;
    }

    hasChildrenSharingPermission(): boolean {
        const children = this.page()?.children;
        if (children?.length > 0) {
            return children.some(child => {
                return this.hasPermission.transform(child.permissions, 'page_sharing_setting');
            });
        }
        return false;
    }



    onAction(type: ActionType, createPageType?: CreatePageType) {
        this.popover.close();
        switch (type) {
            case 'rename':
                if (this.page) {
                    (this.elementRef.nativeElement.closest('.thy-tree-node') as HTMLElement).classList.add('rename-mode');
                    this.treeNodeNameEdit(this.page() as ThyTreeNode);
                }
                break;
            case 'add':
                if (!this.isHeaderActive) return;
                // 这个功能现在由公共组件处理
                break;
            case 'stencil':
                const parentId = this.getParentId(createPageType);
                const afterId = this.getAfterId(createPageType);
                this.pageService.openStencilDetail(undefined, 'page-create', this.viewContainerRef, parentId, afterId);
                break;
            case 'share':
                this.openPageSharing.emit(this.page());
                break;
        }
    }

    deletePage() {
        const selectedPage = this.pagesStore.getState().entities.filter(pageInfo => pageInfo._id === this.pageId())[0];
        const isToPage = this.page()._id === this.pageId() || selectedPage?.parent_ids.includes(this.page()._id);
        const spaceIdentifier = this.currentSpace.identifier;
        this.pageService.deletePage(spaceIdentifier, this.page(), isToPage);
    }

    updateNodeWrapperClass() {
        const moreClass = 'more-active';
        const wrapDOM = this.origin?.closest('.thy-tree-node-wrapper');
        if (this.isAIWritingActive() || this.isActiveMenuActive()) {
            if (wrapDOM) {
                wrapDOM.classList.add(moreClass);
            }
        } else {
            if (wrapDOM) {
                wrapDOM.classList.remove(moreClass);
            }
        }
    }

    openActionMenu(event: Event) {
        if (this.popover) {
            this.popover.close();
        }
        if (this.route.snapshot.firstChild) {
            this.isDraft.set(this.route.snapshot.firstChild.routeConfig.path.includes('draft'));
        }
        this.origin = event.currentTarget as HTMLElement;
        const popoverRef = this.popover.open(this.actionMenuComponent, {
            origin: this.origin,
            hasBackdrop: false,
            outsideClosable: true,
            insideClosable: true,
            manualClosure: true,
            offset: this.isHeaderActive ? 9 : 0,
            placement: 'bottomLeft',
            originActiveClass: undefined
        });
        this.isActiveMenuActive.set(true);

        if (popoverRef) {
            popoverRef.afterClosed().subscribe(() => {
                this.isActiveMenuActive.set(false);
            });
        }

        this.addMouseUpListener(popoverRef);
    }

    addMouseUpListener(popoverRef?: ThyPopoverRef<any>) {
        this.ngZone.runOutsideAngular(() => {
            fromEvent<MouseEvent>(document, 'mouseup')
                .pipe(take(1))
                .subscribe(() => {
                    this.ngZone.run(() => {
                        if (popoverRef) {
                            popoverRef.close();
                        }
                    });
                });
        });
    }

    newWindowOpen() {
        const id = this.page().short_id ?? this.page()._id;
        this.pageService.newWindowOpenPage(this.currentSpace.identifier, id);
    }

    private getAfterId(createPageType?: CreatePageType): string | null | undefined {
        let afterId: string | null | undefined = undefined;
        if (createPageType && createPageType !== CreatePageType.Child) {
            afterId = getAfterId(this.page(), createPageType, this.pageNodes);
        }
        return afterId;
    }

    handleAddPage(page: PageInfo) {
        // 处理添加页面的逻辑，可以根据需要添加额外的处理
        this.selectImportPage.emit(page._id);
    }

    handleAddGroup(_page: PageInfo, draftGroup: DraftInfo) {
        // 处理添加分组的逻辑
        this.pagesStore.addDraftGroup(draftGroup);
    }
}
